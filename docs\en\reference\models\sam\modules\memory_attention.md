---
description: Explore detailed documentation of various SAM 2 encoder modules such as Memory<PERSON><PERSON><PERSON><PERSON>ayer, MemoryAttention, available in Ultralytics' repository.
keywords: Ultralytics, SAM 2 encoder, MemoryAttentionLayer, MemoryAttention
---

# Reference for `ultralytics/models/sam/modules/memory_attention.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/modules/memory_attention.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/modules/memory_attention.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/sam/modules/memory_attention.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.sam.modules.memory_attention.MemoryAttentionLayer

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.memory_attention.MemoryAttention

<br><br>
