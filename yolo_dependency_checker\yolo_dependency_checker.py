# -*- coding: utf-8 -*-
"""
YOLO依赖检查与安装插件

这个Excel插件用于检查和安装YOLO训练所需的依赖项。
它可以帮助确保所有必要的Python包都已正确安装，
从而避免在运行训练脚本时出现依赖项缺失的错误。
"""

import os
import sys
import subprocess
import pkg_resources
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import xlwings as xw
import pandas as pd
from datetime import datetime


class YoloDependencyChecker:
    def __init__(self, master):
        self.master = master
        self.master.title("YOLO依赖检查与安装插件")
        self.master.geometry("800x600")
        self.master.resizable(True, True)
        
        # 设置样式
        self.style = ttk.Style()
        self.style.configure("TButton", font=("Arial", 10))
        self.style.configure("TLabel", font=("Arial", 10))
        self.style.configure("Header.TLabel", font=("Arial", 12, "bold"))
        
        # 创建主框架
        self.main_frame = ttk.Frame(self.master, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建标题
        self.title_label = ttk.Label(
            self.main_frame, 
            text="YOLO依赖检查与安装工具", 
            style="Header.TLabel"
        )
        self.title_label.pack(pady=10)
        
        # 创建选项卡控件
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 创建各个选项卡
        self.check_tab = ttk.Frame(self.notebook)
        self.install_tab = ttk.Frame(self.notebook)
        self.report_tab = ttk.Frame(self.notebook)
        self.settings_tab = ttk.Frame(self.notebook)
        
        self.notebook.add(self.check_tab, text="依赖检查")
        self.notebook.add(self.install_tab, text="安装依赖")
        self.notebook.add(self.report_tab, text="生成报告")
        self.notebook.add(self.settings_tab, text="设置")
        
        # 初始化各选项卡内容
        self.init_check_tab()
        self.init_install_tab()
        self.init_report_tab()
        self.init_settings_tab()
        
        # 初始化数据
        self.installed_packages = {}
        self.required_packages = {}
        self.missing_packages = {}
        self.requirements_file = ""
        self.python_executable = sys.executable
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        self.status_bar = ttk.Label(
            self.master, 
            textvariable=self.status_var, 
            relief=tk.SUNKEN, 
            anchor=tk.W
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 绑定关闭事件
        self.master.protocol("WM_DELETE_WINDOW", self.on_close)
    
    def init_check_tab(self):
        """初始化依赖检查选项卡"""
        # 创建框架
        frame = ttk.Frame(self.check_tab, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 选择requirements文件
        file_frame = ttk.Frame(frame)
        file_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(file_frame, text="依赖文件:").pack(side=tk.LEFT, padx=5)
        
        self.req_file_var = tk.StringVar()
        req_file_entry = ttk.Entry(file_frame, textvariable=self.req_file_var, width=50)
        req_file_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        browse_btn = ttk.Button(file_frame, text="浏览...", command=self.browse_req_file)
        browse_btn.pack(side=tk.LEFT, padx=5)
        
        # 默认选择项目中的requirements.txt
        default_req = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "requirements.txt")
        if os.path.exists(default_req):
            self.req_file_var.set(default_req)
        
        # 检查按钮
        check_btn = ttk.Button(frame, text="检查依赖", command=self.check_dependencies)
        check_btn.pack(pady=10)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(frame, text="检查结果", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 创建表格
        columns = ("包名", "已安装版本", "要求版本", "状态")
        self.result_tree = ttk.Treeview(result_frame, columns=columns, show="headings")
        
        # 设置列标题
        for col in columns:
            self.result_tree.heading(col, text=col)
            self.result_tree.column(col, width=100)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.result_tree.yview)
        self.result_tree.configure(yscrollcommand=scrollbar.set)
        
        # 放置表格和滚动条
        self.result_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def init_install_tab(self):
        """初始化安装依赖选项卡"""
        # 创建框架
        frame = ttk.Frame(self.install_tab, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 选择Python解释器
        py_frame = ttk.Frame(frame)
        py_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(py_frame, text="Python解释器:").pack(side=tk.LEFT, padx=5)
        
        self.python_var = tk.StringVar(value=sys.executable)
        py_entry = ttk.Entry(py_frame, textvariable=self.python_var, width=50)
        py_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        browse_py_btn = ttk.Button(py_frame, text="浏览...", command=self.browse_python)
        browse_py_btn.pack(side=tk.LEFT, padx=5)
        
        # 缺失依赖列表
        missing_frame = ttk.LabelFrame(frame, text="缺失依赖", padding="10")
        missing_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 创建表格
        columns = ("包名", "要求版本")
        self.missing_tree = ttk.Treeview(missing_frame, columns=columns, show="headings")
        
        # 设置列标题
        for col in columns:
            self.missing_tree.heading(col, text=col)
            self.missing_tree.column(col, width=100)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(missing_frame, orient=tk.VERTICAL, command=self.missing_tree.yview)
        self.missing_tree.configure(yscrollcommand=scrollbar.set)
        
        # 放置表格和滚动条
        self.missing_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 安装按钮
        btn_frame = ttk.Frame(frame)
        btn_frame.pack(fill=tk.X, pady=10)
        
        install_selected_btn = ttk.Button(btn_frame, text="安装选中项", command=lambda: self.install_packages(selected_only=True))
        install_selected_btn.pack(side=tk.LEFT, padx=5)
        
        install_all_btn = ttk.Button(btn_frame, text="安装所有缺失依赖", command=lambda: self.install_packages(selected_only=False))
        install_all_btn.pack(side=tk.LEFT, padx=5)
        
        # 安装输出区域
        output_frame = ttk.LabelFrame(frame, text="安装输出", padding="10")
        output_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        self.install_output = tk.Text(output_frame, wrap=tk.WORD, height=10)
        self.install_output.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        output_scrollbar = ttk.Scrollbar(output_frame, orient=tk.VERTICAL, command=self.install_output.yview)
        self.install_output.configure(yscrollcommand=output_scrollbar.set)
        output_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def init_report_tab(self):
        """初始化生成报告选项卡"""
        # 创建框架
        frame = ttk.Frame(self.report_tab, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 报告选项
        options_frame = ttk.LabelFrame(frame, text="报告选项", padding="10")
        options_frame.pack(fill=tk.X, pady=10)
        
        # 报告类型
        type_frame = ttk.Frame(options_frame)
        type_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(type_frame, text="报告类型:").pack(side=tk.LEFT, padx=5)
        
        self.report_type_var = tk.StringVar(value="Excel")
        excel_radio = ttk.Radiobutton(type_frame, text="Excel", variable=self.report_type_var, value="Excel")
        excel_radio.pack(side=tk.LEFT, padx=5)
        
        csv_radio = ttk.Radiobutton(type_frame, text="CSV", variable=self.report_type_var, value="CSV")
        csv_radio.pack(side=tk.LEFT, padx=5)
        
        # 报告内容选项
        content_frame = ttk.Frame(options_frame)
        content_frame.pack(fill=tk.X, pady=5)
        
        self.include_all_var = tk.BooleanVar(value=True)
        all_check = ttk.Checkbutton(content_frame, text="包含所有依赖", variable=self.include_all_var)
        all_check.pack(side=tk.LEFT, padx=5)
        
        self.include_missing_var = tk.BooleanVar(value=True)
        missing_check = ttk.Checkbutton(content_frame, text="仅包含缺失依赖", variable=self.include_missing_var)
        missing_check.pack(side=tk.LEFT, padx=5)
        
        # 报告保存位置
        save_frame = ttk.Frame(frame)
        save_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(save_frame, text="保存位置:").pack(side=tk.LEFT, padx=5)
        
        self.save_path_var = tk.StringVar()
        save_entry = ttk.Entry(save_frame, textvariable=self.save_path_var, width=50)
        save_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        browse_save_btn = ttk.Button(save_frame, text="浏览...", command=self.browse_save_path)
        browse_save_btn.pack(side=tk.LEFT, padx=5)
        
        # 生成报告按钮
        generate_btn = ttk.Button(frame, text="生成报告", command=self.generate_report)
        generate_btn.pack(pady=10)
        
        # 报告预览区域
        preview_frame = ttk.LabelFrame(frame, text="报告预览", padding="10")
        preview_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        self.preview_text = tk.Text(preview_frame, wrap=tk.WORD, height=10)
        self.preview_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        preview_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=self.preview_text.yview)
        self.preview_text.configure(yscrollcommand=preview_scrollbar.set)
        preview_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def init_settings_tab(self):
        """初始化设置选项卡"""
        # 创建框架
        frame = ttk.Frame(self.settings_tab, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 常用依赖文件列表
        files_frame = ttk.LabelFrame(frame, text="常用依赖文件", padding="10")
        files_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 项目根目录
        root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # 查找所有requirements文件
        req_files = []
        for root, dirs, files in os.walk(root_dir):
            for file in files:
                if "requirement" in file.lower() and file.endswith(".txt"):
                    req_files.append(os.path.join(root, file))
        
        # 创建列表框
        self.files_listbox = tk.Listbox(files_frame, height=10)
        self.files_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 添加文件到列表
        for file in req_files:
            self.files_listbox.insert(tk.END, file)
        
        # 添加滚动条
        files_scrollbar = ttk.Scrollbar(files_frame, orient=tk.VERTICAL, command=self.files_listbox.yview)
        self.files_listbox.configure(yscrollcommand=files_scrollbar.set)
        files_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定双击事件
        self.files_listbox.bind("<Double-1>", self.select_req_file)
        
        # 其他设置
        other_frame = ttk.LabelFrame(frame, text="其他设置", padding="10")
        other_frame.pack(fill=tk.X, pady=10)
        
        # 自动检查更新
        self.auto_check_var = tk.BooleanVar(value=True)
        auto_check = ttk.Checkbutton(other_frame, text="启动时自动检查依赖", variable=self.auto_check_var)
        auto_check.pack(anchor=tk.W, pady=5)
        
        # 使用代理
        proxy_frame = ttk.Frame(other_frame)
        proxy_frame.pack(fill=tk.X, pady=5)
        
        self.use_proxy_var = tk.BooleanVar(value=False)
        use_proxy = ttk.Checkbutton(proxy_frame, text="使用代理", variable=self.use_proxy_var)
        use_proxy.pack(side=tk.LEFT, padx=5)
        
        ttk.Label(proxy_frame, text="代理地址:").pack(side=tk.LEFT, padx=5)
        
        self.proxy_var = tk.StringVar()
        proxy_entry = ttk.Entry(proxy_frame, textvariable=self.proxy_var, width=30)
        proxy_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        # 保存设置按钮
        save_settings_btn = ttk.Button(frame, text="保存设置", command=self.save_settings)
        save_settings_btn.pack(pady=10)
    
    def browse_req_file(self):
        """浏览并选择requirements文件"""
        filename = filedialog.askopenfilename(
            title="选择依赖文件",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.req_file_var.set(filename)
    
    def browse_python(self):
        """浏览并选择Python解释器"""
        filename = filedialog.askopenfilename(
            title="选择Python解释器",
            filetypes=[("Executable files", "*.exe"), ("All files", "*.*")]
        )
        if filename:
            self.python_var.set(filename)
    
    def browse_save_path(self):
        """浏览并选择报告保存位置"""
        if self.report_type_var.get() == "Excel":
            filename = filedialog.asksaveasfilename(
                title="保存报告",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
            )
        else:
            filename = filedialog.asksaveasfilename(
                title="保存报告",
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
            )
        if filename:
            self.save_path_var.set(filename)
    
    def select_req_file(self, event):
        """从列表中选择requirements文件"""
        selection = self.files_listbox.curselection()
        if selection:
            filename = self.files_listbox.get(selection[0])
            self.req_file_var.set(filename)
            # 切换到依赖检查选项卡
            self.notebook.select(0)
    
    def check_dependencies(self):
        """检查依赖项"""
        req_file = self.req_file_var.get()
        if not req_file or not os.path.exists(req_file):
            messagebox.showerror("错误", "请选择有效的依赖文件")
            return
        
        self.status_var.set("正在检查依赖...")
        self.master.update_idletasks()
        
        # 清空表格
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)
        
        # 获取已安装的包
        self.installed_packages = {pkg.key: pkg.version for pkg in pkg_resources.working_set}
        
        # 读取requirements文件
        self.requirements_file = req_file
        self.required_packages = {}
        self.missing_packages = {}
        
        try:
            with open(req_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    # 跳过注释和空行
                    if not line or line.startswith('#'):
                        continue
                    
                    # 解析包名和版本要求
                    if '>=' in line:
                        pkg_name, version = line.split('>=', 1)
                        version = '>=' + version
                    elif '==' in line:
                        pkg_name, version = line.split('==', 1)
                        version = '==' + version
                    elif '<=' in line:
                        pkg_name, version = line.split('<=', 1)
                        version = '<=' + version
                    elif '>' in line:
                        pkg_name, version = line.split('>', 1)
                        version = '>' + version
                    elif '<' in line:
                        pkg_name, version = line.split('<', 1)
                        version = '<' + version
                    else:
                        pkg_name = line
                        version = "任意版本"
                    
                    pkg_name = pkg_name.strip().lower()
                    self.required_packages[pkg_name] = version.strip()
                    
                    # 检查是否已安装
                    if pkg_name in self.installed_packages:
                        status = "已安装"
                    else:
                        status = "未安装"
                        self.missing_packages[pkg_name] = version.strip()
                    
                    # 添加到表格
                    self.result_tree.insert(
                        "", 
                        tk.END, 
                        values=(
                            pkg_name, 
                            self.installed_packages.get(pkg_name, "未安装"), 
                            version, 
                            status
                        )
                    )
            
            # 更新缺失依赖表格
            self.update_missing_tree()
            
            # 更新状态
            if self.missing_packages:
                self.status_var.set(f"检查完成，发现{len(self.missing_packages)}个缺失依赖")
            else:
                self.status_var.set("检查完成，所有依赖已安装")
        
        except Exception as e:
            messagebox.showerror("错误", f"检查依赖时出错：{str(e)}")
            self.status_var.set("检查失败")
    
    def update_missing_tree(self):
        """更新缺失依赖表格"""
        # 清空表格
        for item in self.missing_tree.get_children():
            self.missing_tree.delete(item)
        
        # 添加缺失依赖
        for pkg_name, version in self.missing_packages.items():
            self.missing_tree.insert("", tk.END, values=(pkg_name, version))
    
    def install_packages(self, selected_only=False):
        """安装缺失的依赖项"""
        if not self.missing_packages:
            messagebox.showinfo("提示", "没有缺失的依赖项需要安装")
            return
        
        # 获取要安装的包
        packages_to_install = {}
        if selected_only:
            selection = self.missing_tree.selection()
            if not selection:
                messagebox.showinfo("提示", "请选择要安装的依赖项")
                return
            
            for item in selection:
                values = self.missing_tree.item(item, "values")
                pkg_name = values[0]
                version = values[1]
                packages_to_install[pkg_name] = version
        else:
            packages_to_install = self.missing_packages
        
        # 确认安装
        if not messagebox.askyesno("确认", f"确定要安装{len(packages_to_install)}个依赖项吗？"):
            return
        
        # 清空输出区域
        self.install_output.delete(1.0, tk.END)
        self.install_output.insert(tk.END, "开始安装依赖项...\n")
        self.status_var.set("正在安装依赖...")
        self.master.update_idletasks()
        
        # 在新线程中安装依赖
        threading.Thread(target=self._install_thread, args=(packages_to_install,)).start()
    
    def _install_thread(self, packages_to_install):
        """在新线程中安装依赖"""
        python_exe = self.python_var.get()
        
        # 检查Python解释器
        if not os.path.exists(python_exe):
            self._update_output("错误：Python解释器路径无效\n")
            self.status_var.set("安装失败")
            return
        
        # 安装每个包
        success_count = 0
        for pkg_name, version in packages_to_install.items():
            # 构建安装命令
            if version and version != "任意版本":
                if version.startswith(">") or version.startswith("<") or version.startswith("=="):
                    install_spec = f"{pkg_name}{version}"
                else:
                    install_spec = f"{pkg_name}=={version}"
            else:
                install_spec = pkg_name
            
            # 设置代理
            env = os.environ.copy()
            if self.use_proxy_var.get() and self.proxy_var.get():
                env["HTTP_PROXY"] = self.proxy_var.get()
                env["HTTPS_PROXY"] = self.proxy_var.get()
            
            # 执行安装命令
            cmd = [python_exe, "-m", "pip", "install", install_spec]
            self._update_output(f"执行命令: {' '.join(cmd)}\n")
            
            try:
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    text=True,
                    env=env
                )
                
                # 实时显示输出
                for line in process.stdout:
                    self._update_output(line)
                
                process.wait()
                
                if process.returncode == 0:
                    self._update_output(f"✅ {pkg_name} 安装成功\n")
                    success_count += 1
                else:
                    self._update_output(f"❌ {pkg_name} 安装失败\n")
            
            except Exception as e:
                self._update_output(f"安装 {pkg_name} 时出错: {str(e)}\n")
        
        # 安装完成后更新状态
        if success_count == len(packages_to_install):
            self._update_output("所有依赖项安装完成！\n")
            self.status_var.set("安装完成")
        else:
            self._update_output(f"安装完成，{success_count}/{len(packages_to_install)} 个依赖项安装成功\n")
            self.status_var.set(f"部分安装完成 ({success_count}/{len(packages_to_install)})")
        
        # 重新检查依赖
        self.master.after(1000, self.check_dependencies)
    
    def _update_output(self, text):
        """更新安装输出区域"""
        self.install_output.insert(tk.END, text)
        self.install_output.see(tk.END)
        self.master.update_idletasks()
    
    def generate_report(self):
        """生成依赖报告"""
        if not self.required_packages:
            messagebox.showerror("错误", "请先检查依赖")
            return
        
        save_path = self.save_path_var.get()
        if not save_path:
            messagebox.showerror("错误", "请选择报告保存位置")
            return
        
        # 准备报告数据
        report_data = []
        
        if self.include_all_var.get():
            # 包含所有依赖
            for pkg_name, req_version in self.required_packages.items():
                installed_version = self.installed_packages.get(pkg_name, "未安装")
                status = "已安装" if pkg_name in self.installed_packages else "未安装"
                report_data.append({
                    "包名": pkg_name,
                    "已安装版本": installed_version,
                    "要求版本": req_version,
                    "状态": status
                })
        elif self.include_missing_var.get():
            # 仅包含缺失依赖
            for pkg_name, req_version in self.missing_packages.items():
                report_data.append({
                    "包名": pkg_name,
                    "已安装版本": "未安装",
                    "要求版本": req_version,
                    "状态": "未安装"
                })
        
        # 创建DataFrame
        df = pd.DataFrame(report_data)
        
        # 生成报告
        try:
            if self.report_type_var.get() == "Excel":
                # 生成Excel报告
                with pd.ExcelWriter(save_path, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name="依赖报告", index=False)
                    
                    # 添加摘要信息
                    summary_data = {
                        "项目": ["检查时间", "依赖文件", "总依赖数", "已安装数", "缺失数"],
                        "值": [
                            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            self.requirements_file,
                            len(self.required_packages),
                            len(self.required_packages) - len(self.missing_packages),
                            len(self.missing_packages)
                        ]
                    }
                    pd.DataFrame(summary_data).to_excel(writer, sheet_name="摘要", index=False)
            else:
                # 生成CSV报告
                df.to_csv(save_path, index=False)
            
            # 显示预览
            self.preview_text.delete(1.0, tk.END)
            self.preview_text.insert(tk.END, f"报告已保存到: {save_path}\n\n")
            self.preview_text.insert(tk.END, f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            self.preview_text.insert(tk.END, f"依赖文件: {self.requirements_file}\n")
            self.preview_text.insert(tk.END, f"总依赖数: {len(self.required_packages)}\n")
            self.preview_text.insert(tk.END, f"已安装数: {len(self.required_packages) - len(self.missing_packages)}\n")
            self.preview_text.insert(tk.END, f"缺失数: {len(self.missing_packages)}\n\n")
            
            # 显示表格预览
            self.preview_text.insert(tk.END, df.to_string(index=False))
            
            messagebox.showinfo("成功", "报告生成成功")
            self.status_var.set("报告生成完成")
        
        except Exception as e:
            messagebox.showerror("错误", f"生成报告时出错：{str(e)}")
            self.status_var.set("报告生成失败")
    
    def save_settings(self):
        """保存设置"""
        # 这里可以实现保存设置到配置文件的功能
        messagebox.showinfo("成功", "设置已保存")
    
    def on_close(self):
        """关闭窗口时的处理"""
        if messagebox.askyesno("确认", "确定要退出吗？"):
            self.master.destroy()


def main():
    """主函数"""
    root = tk.Tk()
    app = YoloDependencyChecker(root)
    root.mainloop()


def excel_main():
    """Excel插件入口函数"""
    try:
        # 获取活动的Excel应用
        app = xw.App(visible=True)
        wb = xw.books.active
        
        # 启动GUI
        root = tk.Tk()
        YoloDependencyChecker(root)
        root.mainloop()
        
    except Exception as e:
        messagebox.showerror("错误", f"启动插件时出错：{str(e)}")
    finally:
        # 确保Excel应用不会被关闭
        app.quit()


if __name__ == "__main__":
    main()