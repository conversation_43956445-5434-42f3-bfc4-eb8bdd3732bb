#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试脚本 - 验证所有修复是否正常工作
"""

import sys
import os

# 应用相同的路径修复
anaconda_path = r'C:\Users\<USER>\anaconda3\Lib\site-packages'
if anaconda_path not in sys.path:
    sys.path.insert(0, anaconda_path)

user_packages_path = r'C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages'
if user_packages_path in sys.path:
    sys.path.remove(user_packages_path)

def test_imports():
    """测试所有必要的包导入"""
    print("=== 测试包导入 ===")
    
    try:
        import numpy as np
        print(f"✓ NumPy导入成功，版本: {np.__version__}")
    except ImportError as e:
        print(f"✗ NumPy导入失败: {e}")
        return False
    
    try:
        import cv2
        print(f"✓ OpenCV导入成功，版本: {cv2.__version__}")
    except ImportError as e:
        print(f"✗ OpenCV导入失败: {e}")
        return False
    
    try:
        from ultralytics import YOLO
        print("✓ Ultralytics导入成功")
    except ImportError as e:
        print(f"✗ Ultralytics导入失败: {e}")
        return False
    
    try:
        from PyQt5.QtWidgets import QApplication
        print("✓ PyQt5导入成功")
    except ImportError as e:
        print(f"✗ PyQt5导入失败: {e}")
        return False
    
    return True

def test_opencv_functionality():
    """测试OpenCV基本功能"""
    print("\n=== 测试OpenCV功能 ===")
    
    try:
        import cv2
        import numpy as np
        
        # 创建测试图像
        test_img = np.zeros((100, 100, 3), dtype=np.uint8)
        print("✓ 创建测试图像成功")
        
        # 测试基本OpenCV操作
        gray = cv2.cvtColor(test_img, cv2.COLOR_BGR2GRAY)
        print("✓ 颜色空间转换成功")
        
        # 测试图像处理
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        print("✓ 图像模糊处理成功")
        
        return True
        
    except Exception as e:
        print(f"✗ OpenCV功能测试失败: {e}")
        return False

def test_yolo_model():
    """测试YOLO模型加载"""
    print("\n=== 测试YOLO模型 ===")
    
    try:
        from ultralytics import YOLO
        
        # 检查模型文件是否存在
        model_files = ['yolo11n.pt', 'yolo11s.pt', 'yolo11m.pt']
        model_path = None
        
        for model in model_files:
            if os.path.exists(model):
                model_path = model
                break
        
        if not model_path:
            print("✗ 找不到YOLO模型文件")
            return False
        
        # 加载模型
        model = YOLO(model_path)
        print(f"✓ YOLO模型加载成功: {model_path}")
        
        return True
        
    except Exception as e:
        print(f"✗ YOLO模型测试失败: {e}")
        return False

def test_image_detection():
    """测试图像检测功能"""
    print("\n=== 测试图像检测 ===")
    
    try:
        from ultralytics import YOLO
        import cv2
        import numpy as np
        
        # 检查测试图像
        if not os.path.exists('test.jpg'):
            print("✗ 测试图像 test.jpg 不存在")
            return False
        
        # 加载模型
        model_files = ['yolo11n.pt', 'yolo11s.pt', 'yolo11m.pt']
        model_path = None
        
        for model in model_files:
            if os.path.exists(model):
                model_path = model
                break
        
        if not model_path:
            print("✗ 找不到YOLO模型文件")
            return False
        
        model = YOLO(model_path)
        
        # 进行检测
        results = model.predict('test.jpg', conf=0.25, verbose=False)
        
        if results:
            print(f"✓ 图像检测成功，处理了 {len(results)} 个结果")
            
            # 检查检测结果
            for i, result in enumerate(results):
                if result.boxes is not None:
                    num_detections = len(result.boxes)
                    print(f"  图像 {i+1}: 检测到 {num_detections} 个对象")
                else:
                    print(f"  图像 {i+1}: 未检测到对象")
            
            return True
        else:
            print("✗ 检测返回空结果")
            return False
        
    except Exception as e:
        print(f"✗ 图像检测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始最终测试...")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    tests = [
        ("包导入测试", test_imports),
        ("OpenCV功能测试", test_opencv_functionality),
        ("YOLO模型测试", test_yolo_model),
        ("图像检测测试", test_image_detection),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"运行: {test_name}")
        print('='*50)
        
        if test_func():
            passed += 1
            print(f"✓ {test_name} 通过")
        else:
            print(f"✗ {test_name} 失败")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！系统已准备就绪。")
        return True
    else:
        print("❌ 部分测试失败，请检查相关问题。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
