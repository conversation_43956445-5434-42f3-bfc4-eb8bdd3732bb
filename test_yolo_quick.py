#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速YOLO测试
"""

import sys
import os

# 应用路径修复
anaconda_path = r'C:\Users\<USER>\anaconda3\Lib\site-packages'
if anaconda_path not in sys.path:
    sys.path.insert(0, anaconda_path)

user_packages_path = r'C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages'
if user_packages_path in sys.path:
    sys.path.remove(user_packages_path)

print("开始YOLO快速测试...")

try:
    from ultralytics import YOLO
    print("✓ YOLO导入成功")
    
    # 加载模型
    model = YOLO('yolo11n.pt')
    print("✓ 模型加载成功")
    
    # 检查图片
    if os.path.exists('test.jpg'):
        print("✓ 测试图片存在")
        
        # 尝试预测
        print("开始预测...")
        results = model.predict('test.jpg', conf=0.25, verbose=False)
        print(f"✓ 预测成功，结果数量: {len(results)}")
        
        for i, result in enumerate(results):
            if result.boxes is not None:
                num_detections = len(result.boxes)
                print(f"  图片 {i+1}: 检测到 {num_detections} 个对象")
            else:
                print(f"  图片 {i+1}: 未检测到对象")
        
        print("🎉 YOLO测试完全成功！")
    else:
        print("✗ 测试图片不存在")
        
except Exception as e:
    print(f"✗ 测试失败: {e}")
    import traceback
    traceback.print_exc()
