#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的检测测试
"""

import sys
import os

# 应用路径修复
anaconda_path = r'C:\Users\<USER>\anaconda3\Lib\site-packages'
if anaconda_path not in sys.path:
    sys.path.insert(0, anaconda_path)

user_packages_path = r'C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages'
if user_packages_path in sys.path:
    sys.path.remove(user_packages_path)

import numpy as np
import cv2

def test_simple_detection():
    """简单的检测测试"""
    print("开始简单检测测试...")
    
    try:
        from ultralytics import YOLO
        
        # 加载模型
        model = YOLO('yolo11n.pt')
        print("模型加载成功")
        
        # 检查图片
        if not os.path.exists('test.jpg'):
            print("测试图片不存在")
            return False
        
        # 使用OpenCV直接读取图片
        img = cv2.imread('test.jpg')
        if img is None:
            print("无法读取图片")
            return False
        
        print(f"图片读取成功，形状: {img.shape}")
        print(f"图片类型: {type(img)}")
        print(f"图片dtype: {img.dtype}")
        
        # 尝试预测
        print("开始预测...")
        results = model.predict(img, conf=0.25, verbose=False)
        
        print(f"预测成功，结果数量: {len(results)}")
        
        for i, result in enumerate(results):
            if result.boxes is not None:
                num_detections = len(result.boxes)
                print(f"图片 {i+1}: 检测到 {num_detections} 个对象")
            else:
                print(f"图片 {i+1}: 未检测到对象")
        
        return True
        
    except Exception as e:
        print(f"检测失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_simple_detection()
    if success:
        print("✓ 简单检测测试成功")
    else:
        print("✗ 简单检测测试失败")
