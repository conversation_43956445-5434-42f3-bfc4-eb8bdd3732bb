---
description: Learn how to use BaseTrainer in Ultralytics YOLO for efficient model training. Comprehensive guide for configurations, datasets, and optimization.
keywords: Ultralytics, YOLO, BaseTrainer, model training, configuration, datasets, optimization, machine learning
---

# Reference for `ultralytics/engine/trainer.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/engine/trainer.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/engine/trainer.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/engine/trainer.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.engine.trainer.BaseTrainer

<br><br>
