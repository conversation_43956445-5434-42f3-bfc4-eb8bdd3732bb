#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单路径测试
"""

import os
from ultralytics import Y<PERSON><PERSON>

def test_simple_path():
    """使用简单路径测试YOLO"""
    print("使用简单路径测试YOLO...")
    
    # 加载模型
    model = YOLO("yolo11n.pt")
    print("模型加载成功")
    
    # 使用简单路径
    image_path = "test.jpg"
    print(f"测试图片: {image_path}")
    print(f"文件存在: {os.path.exists(image_path)}")
    
    try:
        # 预测
        results = model.predict(image_path, conf=0.25, verbose=False)
        print(f"预测成功，结果数量: {len(results)}")
        
        if len(results) > 0:
            result = results[0]
            print(f"检测到 {len(result.boxes)} 个对象" if result.boxes else "未检测到对象")
            
            # 尝试绘制结果
            try:
                annotated_img = result.plot()
                print(f"绘制成功，图片形状: {annotated_img.shape}")
                
                # 保存结果
                save_path = "result.jpg"
                
                # 使用cv2.imencode和文件写入
                import cv2
                success, encoded_img = cv2.imencode('.jpg', annotated_img)
                if success:
                    with open(save_path, 'wb') as f:
                        f.write(encoded_img.tobytes())
                    print(f"结果已保存至: {save_path}")
                else:
                    print("编码失败")
                
                return True
                
            except Exception as e:
                print(f"绘制失败: {e}")
                import traceback
                traceback.print_exc()
                return False
        else:
            print("预测成功但没有结果")
            return True
            
    except Exception as e:
        print(f"预测失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_simple_path()
    if success:
        print("简单路径测试成功！")
    else:
        print("简单路径测试失败")
