#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试PIL读取图片
"""

import os
import numpy as np
from PIL import Image
import cv2

def test_pil_reading():
    """测试PIL读取图片"""
    image_path = "test.jpg"
    print(f"测试图片: {image_path}")
    print(f"文件存在: {os.path.exists(image_path)}")
    
    try:
        # 使用PIL读取
        with Image.open(image_path) as img:
            print(f"PIL读取成功")
            print(f"图片模式: {img.mode}")
            print(f"图片尺寸: {img.size}")
            
            # 转换为numpy数组
            img_array = np.asarray(img)
            print(f"numpy数组形状: {img_array.shape}")
            print(f"numpy数组类型: {img_array.dtype}")
            
            # 转换颜色空间
            if img.mode == 'RGB':
                img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
                print(f"转换为BGR成功，形状: {img_bgr.shape}")
                return img_bgr
            else:
                print(f"图片模式不是RGB: {img.mode}")
                return img_array
                
    except Exception as e:
        print(f"PIL读取失败: {e}")
        return None

def test_custom_imread():
    """测试自定义的imread函数"""
    from ultralytics.utils.patches import imread
    
    image_path = "test.jpg"
    print(f"\n测试自定义imread函数")
    
    try:
        img = imread(image_path)
        if img is not None:
            print(f"自定义imread成功，形状: {img.shape}")
            return img
        else:
            print("自定义imread返回None")
            return None
    except Exception as e:
        print(f"自定义imread失败: {e}")
        return None

if __name__ == "__main__":
    # 测试PIL
    pil_result = test_pil_reading()
    
    # 测试自定义imread
    custom_result = test_custom_imread()
    
    if pil_result is not None and custom_result is not None:
        print("\n两种方法都成功！")
    elif pil_result is not None:
        print("\n只有PIL成功")
    elif custom_result is not None:
        print("\n只有自定义imread成功")
    else:
        print("\n两种方法都失败")
