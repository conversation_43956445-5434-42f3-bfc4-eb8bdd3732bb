@echo off
echo 正在启动YOLO依赖检查与安装插件...

:: 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python未安装或未添加到PATH环境变量中。
    echo 请安装Python并确保将其添加到PATH环境变量中。
    pause
    exit /b 1
)

:: 检查必要的依赖
python -c "import tkinter" >nul 2>&1
if %errorlevel% neq 0 (
    echo 缺少tkinter模块，这是Python的标准库，应该随Python一起安装。
    echo 请确保您的Python安装包含tkinter。
    pause
    exit /b 1
)

:: 运行安装程序
echo 正在运行安装程序...
python "%~dp0setup.py"

pause