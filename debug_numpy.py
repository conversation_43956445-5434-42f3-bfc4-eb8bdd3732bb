#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试numpy和OpenCV
"""

import numpy as np
import cv2

def debug_numpy():
    """调试numpy数组创建"""
    print("调试numpy数组...")
    
    # 创建数组
    img = np.zeros((640, 640, 3), dtype=np.uint8)
    print(f"数组类型: {type(img)}")
    print(f"数组形状: {img.shape}")
    print(f"数组dtype: {img.dtype}")
    print(f"是否为numpy数组: {isinstance(img, np.ndarray)}")
    
    return img

def debug_opencv():
    """调试OpenCV版本"""
    print(f"OpenCV版本: {cv2.__version__}")
    print(f"OpenCV构建信息:")
    print(cv2.getBuildInformation())

if __name__ == "__main__":
    debug_opencv()
    img = debug_numpy()
    
    # 尝试简单的OpenCV操作
    try:
        print("尝试cv2.rectangle...")
        cv2.rectangle(img, (100, 100), (200, 200), (255, 255, 255), 2)
        print("cv2.rectangle成功")
    except Exception as e:
        print(f"cv2.rectangle失败: {e}")
        
    try:
        print("尝试cv2.imwrite...")
        cv2.imwrite("debug_test.jpg", img)
        print("cv2.imwrite成功")
    except Exception as e:
        print(f"cv2.imwrite失败: {e}")
