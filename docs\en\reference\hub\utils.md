---
description: Explore the utilities in the Ultralytics HUB. Learn about smart_request, request_with_credentials, and more to enhance your YOLO projects.
keywords: Ultralytics, HUB, Utilities, YOLO, smart_request, request_with_credentials
---

# Reference for `ultralytics/hub/utils.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/hub/utils.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/hub/utils.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/hub/utils.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.hub.utils.Events

<br><br><hr><br>

## ::: ultralytics.hub.utils.request_with_credentials

<br><br><hr><br>

## ::: ultralytics.hub.utils.requests_with_progress

<br><br><hr><br>

## ::: ultralytics.hub.utils.smart_request

<br><br>
