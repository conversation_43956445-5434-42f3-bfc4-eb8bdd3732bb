#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试环境脚本
"""

import sys
print("Python executable:", sys.executable)
print("Python version:", sys.version)
print("Python path:")
for p in sys.path:
    print(f"  {p}")

print("\n尝试导入numpy...")
try:
    import numpy
    print(f"✓ numpy导入成功，版本: {numpy.__version__}")
    print(f"  numpy路径: {numpy.__file__}")
except ImportError as e:
    print(f"✗ numpy导入失败: {e}")

print("\n尝试导入cv2...")
try:
    import cv2
    print(f"✓ cv2导入成功，版本: {cv2.__version__}")
    print(f"  cv2路径: {cv2.__file__}")
except ImportError as e:
    print(f"✗ cv2导入失败: {e}")

print("\n尝试导入ultralytics...")
try:
    from ultralytics import YOLO
    print("✓ ultralytics导入成功")
except ImportError as e:
    print(f"✗ ultralytics导入失败: {e}")

print("\n环境测试完成！")
