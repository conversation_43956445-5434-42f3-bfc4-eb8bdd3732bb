@echo off
echo 正在启动YOLO依赖快速检查...

:: 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python未安装或未添加到PATH环境变量中。
    echo 请安装Python并确保将其添加到PATH环境变量中。
    pause
    exit /b 1
)

:: 检查必要的依赖
python -c "import tkinter" >nul 2>&1
if %errorlevel% neq 0 (
    echo 缺少tkinter模块，这是Python的标准库，应该随Python一起安装。
    echo 请确保您的Python安装包含tkinter。
    pause
    exit /b 1
)

:: 尝试安装必要的依赖
echo 正在检查并安装必要的依赖...
python -m pip install openpyxl pandas xlwings

:: 直接运行依赖检查器
echo 正在启动依赖检查器...
python "%~dp0yolo_dependency_checker.py"

pause