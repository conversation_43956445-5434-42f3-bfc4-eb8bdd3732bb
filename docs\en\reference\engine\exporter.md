---
description: Learn how to export YOLOv8 models to formats like ONNX, TensorRT, CoreML, and more. Optimize your exports for different platforms.
keywords: YOLOv8, export formats, ONNX, TensorRT, CoreML, machine learning model export, AI, deep learning
---

# Reference for `ultralytics/engine/exporter.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/engine/exporter.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/engine/exporter.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/engine/exporter.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.engine.exporter.Exporter

<br><br><hr><br>

## ::: ultralytics.engine.exporter.IOSDetectModel

<br><br><hr><br>

## ::: ultralytics.engine.exporter.NMSModel

<br><br><hr><br>

## ::: ultralytics.engine.exporter.export_formats

<br><br><hr><br>

## ::: ultralytics.engine.exporter.validate_args

<br><br><hr><br>

## ::: ultralytics.engine.exporter.gd_outputs

<br><br><hr><br>

## ::: ultralytics.engine.exporter.try_export

<br><br>
