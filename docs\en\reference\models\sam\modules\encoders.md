---
description: Explore detailed documentation of various SAM encoder modules such as ImageEncoderViT, PromptEncoder, and more, available in Ultralytics' repository.
keywords: Ultralytics, SAM encoder, ImageEncoderViT, PromptEncoder, PositionEmbeddingRandom, Block, Attention, PatchEmbed
---

# Reference for `ultralytics/models/sam/modules/encoders.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/modules/encoders.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/modules/encoders.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/sam/modules/encoders.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.sam.modules.encoders.ImageEncoderViT

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.encoders.PromptEncoder

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.encoders.MemoryEncoder

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.encoders.ImageEncoder

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.encoders.FpnNeck

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.encoders.Hiera

<br><br>
