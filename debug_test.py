#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试测试脚本
"""

import cv2
import numpy as np
from ultralytics import YOL<PERSON>

def test_image_loading():
    """测试图片加载"""
    image_path = "datasets/images/test/1616.rf.313fed755a67aebe1734991d7a4c3b81.jpg"
    
    print(f"测试图片路径: {image_path}")
    
    # 方法1: 使用cv2.imread
    print("方法1: 使用cv2.imread")
    img1 = cv2.imread(image_path)
    if img1 is not None:
        print(f"成功读取图片，形状: {img1.shape}")
    else:
        print("cv2.imread 失败")
    
    # 方法2: 使用numpy和cv2.imdecode
    print("方法2: 使用numpy和cv2.imdecode")
    try:
        with open(image_path, 'rb') as f:
            file_bytes = np.frombuffer(f.read(), np.uint8)
        if file_bytes.size > 0:
            img2 = cv2.imdecode(file_bytes, cv2.IMREAD_COLOR)
            if img2 is not None:
                print(f"成功读取图片，形状: {img2.shape}")
            else:
                print("cv2.imdecode 失败")
        else:
            print("文件内容为空")
    except Exception as e:
        print(f"方法2出错: {e}")

def test_yolo_model():
    """测试YOLO模型"""
    print("\n测试YOLO模型加载...")
    try:
        model = YOLO("yolo11n.pt")
        print("YOLO模型加载成功")
        
        # 测试预测
        image_path = "datasets/images/test/1616.rf.313fed755a67aebe1734991d7a4c3b81.jpg"
        print(f"测试预测: {image_path}")
        
        # 直接使用路径
        print("方法1: 直接使用路径")
        try:
            results = model.predict(image_path, conf=0.25, verbose=False)
            print(f"预测成功，结果数量: {len(results)}")
        except Exception as e:
            print(f"路径预测失败: {e}")
        
        # 使用numpy数组
        print("方法2: 使用numpy数组")
        try:
            img = cv2.imread(image_path)
            if img is not None:
                results = model.predict(img, conf=0.25, verbose=False)
                print(f"预测成功，结果数量: {len(results)}")
            else:
                print("无法读取图片")
        except Exception as e:
            print(f"数组预测失败: {e}")
            
    except Exception as e:
        print(f"YOLO模型测试失败: {e}")

if __name__ == "__main__":
    test_image_loading()
    test_yolo_model()
