# YOLOv11 Detection System Installation and Startup Script
Write-Host "YOLOv11 Detection System Installation and Startup Script" -ForegroundColor Green
Write-Host "====================================================" -ForegroundColor Green

# Check if Python is installed
try {
    $pythonVersion = python --version 2>&1
    Write-Host "[INFO] Python detected: $pythonVersion" -ForegroundColor Cyan
} catch {
    Write-Host "[ERROR] Python not detected. Please install Python 3.8 or higher." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if we're in an Anaconda environment
$inAnaconda = $env:CONDA_PREFIX -ne $null
if ($inAnaconda) {
    Write-Host "[INFO] Running in Anaconda environment: $env:CONDA_PREFIX" -ForegroundColor Cyan
}

# Install required dependencies
Write-Host "[INFO] Installing required libraries..." -ForegroundColor Cyan

try {
    pip install ultralytics opencv-python PyQt5 numpy pillow torch torchvision
    Write-Host "[INFO] Dependencies installed successfully." -ForegroundColor Green
} catch {
    Write-Host "[ERROR] Failed to install dependencies. Please check your network connection or install manually." -ForegroundColor Red
    Write-Host "Required dependencies: ultralytics opencv-python PyQt5 numpy pillow torch torchvision" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Check for YOLOv11 model file
if (-not (Test-Path "yolo11n.pt")) {
    Write-Host "[WARNING] Default model file yolo11n.pt not found." -ForegroundColor Yellow
    Write-Host "[INFO] Attempting to download YOLOv8 model as fallback..." -ForegroundColor Cyan
    
    # Use Python to download model
    try {
        python -c "from ultralytics import YOLO; YOLO('yolov8n.pt')"
        Write-Host "[INFO] YOLOv8 model downloaded successfully." -ForegroundColor Green
    } catch {
        Write-Host "[WARNING] Could not download YOLOv8 model. You'll need to select a model manually in the GUI." -ForegroundColor Yellow
    }
    
    Write-Host "[INFO] Please select a model file manually in the GUI." -ForegroundColor Cyan
} else {
    Write-Host "[INFO] Model file yolo11n.pt detected." -ForegroundColor Green
}

# Start GUI
Write-Host "[INFO] Starting YOLOv11 GUI..." -ForegroundColor Cyan
try {
    python yolov11_gui.py
} catch {
    Write-Host "[ERROR] Failed to start YOLOv11 GUI. Error: $_" -ForegroundColor Red
}

Read-Host "Press Enter to exit"