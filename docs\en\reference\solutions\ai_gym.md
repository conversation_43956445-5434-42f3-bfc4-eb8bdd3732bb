---
description: Explore the AI Gym class for real-time pose detection and gym step counting using Ultralytics YOLO. Learn to implement pose estimation effectively.
keywords: Ultralytics, AI Gym, YOLO, pose detection, gym step counting, real-time pose estimation, Python
---

# Reference for `ultralytics/solutions/ai_gym.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/solutions/ai_gym.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/solutions/ai_gym.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/solutions/ai_gym.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.solutions.ai_gym.AIGym

<br><br>
