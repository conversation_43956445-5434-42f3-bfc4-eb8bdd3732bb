---
description: Get to know more about Ultralytics nn.autobackend.check_class_names functionality. Optimize your YOLO models seamlessly.
keywords: Ultralytics, AutoBackend, check_class_names, YOLO, YOLO models, optimization
---

# Reference for `ultralytics/nn/autobackend.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/nn/autobackend.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/nn/autobackend.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/nn/autobackend.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.nn.autobackend.AutoBackend

<br><br><hr><br>

## ::: ultralytics.nn.autobackend.check_class_names

<br><br><hr><br>

## ::: ultralytics.nn.autobackend.default_class_names

<br><br>
