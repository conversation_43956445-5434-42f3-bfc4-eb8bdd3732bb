#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比较不同来源的numpy数组
"""

import cv2
import numpy as np

def compare_arrays():
    """比较不同来源的numpy数组"""
    
    # 方法1：直接创建numpy数组
    print("方法1：直接创建numpy数组")
    img1 = np.zeros((100, 100, 3), dtype=np.uint8)
    print(f"类型: {type(img1)}")
    print(f"dtype: {img1.dtype}")
    print(f"flags: {img1.flags}")
    print(f"base: {img1.base}")
    print(f"__array_interface__: {img1.__array_interface__}")
    
    # 方法2：cv2.imread读取的数组
    print("\n方法2：cv2.imread读取的数组")
    img2 = cv2.imread("test.jpg")
    if img2 is not None:
        print(f"类型: {type(img2)}")
        print(f"dtype: {img2.dtype}")
        print(f"flags: {img2.flags}")
        print(f"base: {img2.base}")
        print(f"__array_interface__: {img2.__array_interface__}")
    
    # 方法3：尝试复制cv2.imread的数组属性
    print("\n方法3：复制cv2.imread数组的属性")
    if img2 is not None:
        img3 = np.empty_like(img2)
        img3[:] = 0
        print(f"类型: {type(img3)}")
        print(f"dtype: {img3.dtype}")
        print(f"flags: {img3.flags}")
        print(f"base: {img3.base}")
        
        # 测试copyMakeBorder
        try:
            result = cv2.copyMakeBorder(img3, 10, 10, 10, 10, cv2.BORDER_CONSTANT, value=(114, 114, 114))
            print("方法3的copyMakeBorder成功")
        except Exception as e:
            print(f"方法3的copyMakeBorder失败: {e}")
    
    # 方法4：使用cv2创建数组
    print("\n方法4：使用cv2创建数组")
    try:
        # 创建一个临时图片文件，然后读取
        temp_img = np.zeros((100, 100, 3), dtype=np.uint8)
        cv2.imwrite("temp.jpg", temp_img)
        img4 = cv2.imread("temp.jpg")
        if img4 is not None:
            print(f"类型: {type(img4)}")
            print(f"dtype: {img4.dtype}")
            print(f"flags: {img4.flags}")
            
            # 测试copyMakeBorder
            try:
                result = cv2.copyMakeBorder(img4, 10, 10, 10, 10, cv2.BORDER_CONSTANT, value=(114, 114, 114))
                print("方法4的copyMakeBorder成功")
            except Exception as e:
                print(f"方法4的copyMakeBorder失败: {e}")
    except Exception as e:
        print(f"方法4失败: {e}")

if __name__ == "__main__":
    compare_arrays()
