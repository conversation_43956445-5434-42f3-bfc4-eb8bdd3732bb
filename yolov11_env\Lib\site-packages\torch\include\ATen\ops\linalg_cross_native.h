#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>
#include <ATen/ops/linalg_cross_meta.h>

namespace at {
namespace native {
struct TORCH_API structured_linalg_cross_out : public at::meta::structured_linalg_cross {
void impl(const at::Tensor & self, const at::Tensor & other, int64_t dim, const at::Tensor & out);
};
TORCH_API at::Tensor linalg_cross_zerotensor(const at::Tensor & self, const at::Tensor & other, int64_t dim=-1);
} // namespace native
} // namespace at
