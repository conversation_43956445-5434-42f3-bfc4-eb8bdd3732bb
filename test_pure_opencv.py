#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
纯OpenCV测试 - 不导入ultralytics
"""

import cv2
import numpy as np
import os

def test_pure_opencv():
    """测试纯OpenCV功能"""
    print("测试纯OpenCV功能...")
    
    # 创建测试图片
    img = np.zeros((640, 640, 3), dtype=np.uint8)
    print(f"数组类型: {type(img)}")
    print(f"数组形状: {img.shape}")
    print(f"数组dtype: {img.dtype}")
    
    # 添加一些形状
    try:
        cv2.rectangle(img, (100, 100), (300, 300), (0, 255, 0), -1)  # 绿色矩形
        cv2.circle(img, (400, 400), 100, (255, 0, 0), -1)  # 蓝色圆形
        print("绘制形状成功")
    except Exception as e:
        print(f"绘制形状失败: {e}")
        return False
    
    # 保存图片
    try:
        test_path = "pure_opencv_test.jpg"
        cv2.imwrite(test_path, img)
        print(f"保存图片成功: {test_path}")
    except Exception as e:
        print(f"保存图片失败: {e}")
        return False
    
    # 读取图片
    try:
        loaded_img = cv2.imread(test_path)
        if loaded_img is not None:
            print(f"读取图片成功，形状: {loaded_img.shape}")
        else:
            print("读取图片失败")
            return False
    except Exception as e:
        print(f"读取图片异常: {e}")
        return False
    
    return True

def test_existing_image():
    """测试读取现有图片"""
    print("\n测试读取现有图片...")
    
    image_path = "datasets/images/test/1616.rf.313fed755a67aebe1734991d7a4c3b81.jpg"
    print(f"图片路径: {image_path}")
    print(f"文件存在: {os.path.exists(image_path)}")
    
    try:
        img = cv2.imread(image_path)
        if img is not None:
            print(f"成功读取图片，形状: {img.shape}")
            return img
        else:
            print("无法读取图片")
            return None
    except Exception as e:
        print(f"读取图片异常: {e}")
        return None

if __name__ == "__main__":
    # 测试纯OpenCV
    success = test_pure_opencv()
    if success:
        print("纯OpenCV测试成功！")
    else:
        print("纯OpenCV测试失败")
    
    # 测试现有图片
    img = test_existing_image()
    if img is not None:
        print("现有图片读取成功！")
    else:
        print("现有图片读取失败")
