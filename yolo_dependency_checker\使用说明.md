# YOLO依赖检查与安装插件使用说明

## 简介

这个Excel插件用于检查和安装YOLO训练所需的依赖项。它可以帮助您确保所有必要的Python包都已正确安装，从而避免在运行训练脚本时出现依赖项缺失的错误，如`No module named 'matplotlib'`等。

## 系统要求

- Windows 7/8/10/11
- Python 3.6 或更高版本
- Microsoft Excel 2013 或更高版本

## 快速开始

### 方法一：快速检查（无需安装）

如果您只想快速检查依赖项，而不想进行完整安装，可以：

1. 双击运行 `quick_check.bat`
2. 程序会自动检查并安装必要的基础依赖（openpyxl, pandas, xlwings）
3. 然后启动依赖检查器
4. 在依赖检查器中，选择您的requirements.txt文件（默认会自动选择项目根目录的requirements.txt）
5. 点击"检查依赖"按钮

### 方法二：完整安装

如果您希望将插件安装到系统中，并创建Excel加载项：

1. 双击运行 `run_dependency_checker.bat`
2. 在安装程序中，您可以：
   - 选择安装路径
   - 选择是否创建Excel加载项
   - 选择是否安装必要的Python依赖
   - 选择是否创建桌面快捷方式
3. 点击"安装"按钮开始安装
4. 安装完成后，您可以通过桌面快捷方式或Excel加载项启动插件

## 使用方法

### 检查依赖

1. 启动插件后，切换到"依赖检查"选项卡
2. 选择要检查的requirements.txt文件
3. 点击"检查依赖"按钮
4. 查看检查结果，包括已安装和未安装的依赖项

### 安装依赖

1. 在检查依赖后，切换到"安装依赖"选项卡
2. 查看缺失的依赖项列表
3. 选择要安装的依赖项（可以选择单个或全部）
4. 点击"安装选中项"或"安装所有缺失依赖"按钮
5. 查看安装输出，了解安装进度和结果

### 生成报告

1. 在检查依赖后，切换到"生成报告"选项卡
2. 选择报告类型（Excel或CSV）
3. 选择报告内容（所有依赖或仅缺失依赖）
4. 选择报告保存位置
5. 点击"生成报告"按钮
6. 查看报告预览，并在保存位置找到生成的报告文件

## 在Excel中使用插件

如果您已经创建了Excel加载项，可以按照以下步骤在Excel中使用插件：

1. 打开Excel
2. 点击"文件" > "选项" > "加载项"
3. 在"管理"下拉菜单中选择"Excel加载项"，然后点击"转到"
4. 确保"YOLO依赖检查器"已勾选，然后点击"确定"
5. 在Excel的功能区中，您应该能看到"加载项"选项卡
6. 点击"加载项"选项卡，然后点击"YOLO依赖检查器"按钮启动插件

## 常见问题解答

### Q: 为什么我在运行YOLO训练脚本时遇到"No module named 'matplotlib'"错误？

A: 这是因为您的Python环境中缺少matplotlib库。您可以使用本插件检查并安装缺失的依赖项，或者手动运行`pip install matplotlib`命令安装。

### Q: 插件安装时提示"Python未安装或未添加到PATH环境变量中"怎么办？

A: 请确保您已经安装了Python，并且在安装时勾选了"Add Python to PATH"选项。如果已经安装但未添加到PATH，您可以手动添加或重新安装Python。

### Q: 我的Excel中没有显示"YOLO依赖检查器"按钮怎么办？

A: 请检查Excel加载项是否正确安装。您可以在Excel的"文件" > "选项" > "加载项"中查看和管理加载项。如果未显示，请尝试重新运行安装程序并选择"创建Excel加载项"选项。

### Q: 安装依赖时出现权限错误怎么办？

A: 请尝试以管理员身份运行插件。右键点击插件快捷方式，选择"以管理员身份运行"。

### Q: 我使用的是虚拟环境，如何确保插件在正确的环境中安装依赖？

A: 在"安装依赖"选项卡中，您可以选择Python解释器路径。请确保选择了虚拟环境中的Python解释器（通常位于虚拟环境目录的Scripts或bin文件夹中）。

## 故障排除

如果您在使用插件时遇到问题，请尝试以下步骤：

1. 确保您的Python和Excel版本符合系统要求
2. 检查是否已安装所有必要的Python依赖（openpyxl, pandas, xlwings）
3. 尝试以管理员身份运行插件
4. 检查Python路径是否正确
5. 如果问题仍然存在，请查看安装输出和错误消息，以获取更多信息

## 联系与支持

如果您有任何问题或建议，请联系项目维护者或在项目仓库中提交issue。