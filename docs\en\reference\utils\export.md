---
description: Learn how to export PyTorch models to ONNX and TensorRT formats using Ultralytics utilities. Comprehensive guide for configurations, dynamic shapes, and precision optimizations.  
keywords: Ultralytics, YOLO, export, ONNX, TensorRT, PyTorch, model conversion, dynamic shapes, FP16, INT8, machine learning
---

# Reference for `ultralytics/utils/export.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/export.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/export.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/export.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.utils.export.export_onnx

<br><br><hr><br>

## ::: ultralytics.utils.export.export_engine

<br><br>
