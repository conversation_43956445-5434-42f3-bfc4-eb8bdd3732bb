#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单YOLO测试
"""

import os
import cv2
import numpy as np

def test_yolo_prediction():
    """测试YOLO预测"""
    print("测试YOLO预测...")
    
    # 导入ultralytics
    from ultralytics import YOLO
    
    # 加载模型
    model = YOLO("yolo11n.pt")
    print("模型加载成功")
    
    # 测试图片路径
    image_path = "datasets/images/test/1616.rf.313fed755a67aebe1734991d7a4c3b81.jpg"
    print(f"测试图片: {image_path}")
    print(f"文件存在: {os.path.exists(image_path)}")
    
    try:
        # 预测
        results = model.predict(image_path, conf=0.25, verbose=False)
        print(f"预测成功，结果数量: {len(results)}")
        
        if len(results) > 0:
            result = results[0]
            print(f"检测到 {len(result.boxes)} 个对象" if result.boxes else "未检测到对象")
            
            # 尝试绘制结果
            try:
                annotated_img = result.plot()
                print(f"绘制成功，图片形状: {annotated_img.shape}")
                
                # 保存结果 - 使用原生cv2.imwrite
                save_path = "yolo_result_simple.jpg"
                
                # 尝试使用numpy保存
                try:
                    # 使用cv2.imencode和tofile方法
                    success, encoded_img = cv2.imencode('.jpg', annotated_img)
                    if success:
                        with open(save_path, 'wb') as f:
                            f.write(encoded_img.tobytes())
                        print(f"结果已保存至: {save_path}")
                    else:
                        print("编码失败")
                except Exception as e:
                    print(f"保存失败: {e}")
                
                return True
                
            except Exception as e:
                print(f"绘制失败: {e}")
                return False
        else:
            print("预测成功但没有结果")
            return True
            
    except Exception as e:
        print(f"预测失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_yolo_prediction()
    if success:
        print("YOLO测试成功！")
    else:
        print("YOLO测试失败")
