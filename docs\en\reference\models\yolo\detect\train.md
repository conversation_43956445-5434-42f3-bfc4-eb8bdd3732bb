---
description: Learn about the DetectionTrainer class for training YOLO models on custom datasets. Discover methods, examples, and more.
keywords: Ultralytics, YOLO, DetectionTrainer, training, object detection, machine learning, build dataset, dataloader, detection model
---

# Reference for `ultralytics/models/yolo/detect/train.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/detect/train.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/detect/train.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/yolo/detect/train.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.yolo.detect.train.DetectionTrainer

<br><br>
