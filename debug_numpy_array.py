#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试numpy数组属性
"""

import numpy as np
import cv2
from ultralytics.utils.patches import imread

def debug_numpy_array():
    """调试numpy数组属性"""
    image_path = "test.jpg"
    
    # 使用自定义imread
    img = imread(image_path)
    if img is None:
        print("imread失败")
        return
    
    print(f"图片形状: {img.shape}")
    print(f"图片类型: {img.dtype}")
    print(f"数组类型: {type(img)}")
    print(f"是否为numpy数组: {isinstance(img, np.ndarray)}")
    print(f"数组flags: {img.flags}")
    print(f"是否连续: {img.flags['C_CONTIGUOUS']}")
    print(f"是否可写: {img.flags['WRITEABLE']}")
    print(f"是否对齐: {img.flags['ALIGNED']}")
    
    # 尝试创建一个标准的numpy数组
    print("\n创建标准numpy数组进行对比:")
    standard_array = np.zeros((640, 640, 3), dtype=np.uint8)
    print(f"标准数组类型: {type(standard_array)}")
    print(f"标准数组flags: {standard_array.flags}")
    
    # 尝试使用copyMakeBorder
    print("\n测试copyMakeBorder:")
    try:
        result = cv2.copyMakeBorder(img, 10, 10, 10, 10, cv2.BORDER_CONSTANT, value=(114, 114, 114))
        print("copyMakeBorder成功")
    except Exception as e:
        print(f"copyMakeBorder失败: {e}")
        
        # 尝试修复数组
        print("尝试修复数组...")
        try:
            # 确保数组是连续的
            img_fixed = np.ascontiguousarray(img, dtype=np.uint8)
            print(f"修复后数组flags: {img_fixed.flags}")
            
            result = cv2.copyMakeBorder(img_fixed, 10, 10, 10, 10, cv2.BORDER_CONSTANT, value=(114, 114, 114))
            print("修复后copyMakeBorder成功")
        except Exception as e2:
            print(f"修复后仍然失败: {e2}")
            
            # 尝试完全重新创建数组
            print("尝试重新创建数组...")
            try:
                img_new = np.array(img, dtype=np.uint8, copy=True)
                print(f"重新创建数组flags: {img_new.flags}")
                
                result = cv2.copyMakeBorder(img_new, 10, 10, 10, 10, cv2.BORDER_CONSTANT, value=(114, 114, 114))
                print("重新创建后copyMakeBorder成功")
            except Exception as e3:
                print(f"重新创建后仍然失败: {e3}")

if __name__ == "__main__":
    debug_numpy_array()
