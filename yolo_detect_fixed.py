#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版的YOLO检测脚本
使用原生的ultralytics CLI接口，避免直接使用有问题的OpenCV函数
"""

import argparse
import os
import subprocess
import sys
from pathlib import Path

def run_yolo_cli(model_path, source, conf=0.25, save=True, show=False, project=None, name=None):
    """
    使用ultralytics的CLI接口运行YOLO检测
    这样可以避免直接使用有问题的OpenCV函数
    """
    cmd = [
        sys.executable, "-m", "ultralytics",
        "detect",
        "predict",
        f"model={model_path}",
        f"source={source}",
        f"conf={conf}",
    ]
    
    if save:
        cmd.append("save=True")
    
    if show:
        cmd.append("show=True")
    
    if project:
        cmd.append(f"project={project}")
    
    if name:
        cmd.append(f"name={name}")
    
    print(f"运行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=os.getcwd())
        
        if result.returncode == 0:
            print("检测成功完成！")
            print("输出:")
            print(result.stdout)
            return True
        else:
            print("检测失败！")
            print("错误信息:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"运行检测时出错: {e}")
        return False

def run_yolo_direct():
    """
    直接使用yolo命令行工具
    """
    parser = argparse.ArgumentParser(description='YOLOv11检测脚本（修复版）')
    parser.add_argument('--model', type=str, default='yolo11n.pt', help='模型文件路径')
    parser.add_argument('--source', type=str, required=True, help='输入源（图片、视频、文件夹）')
    parser.add_argument('--conf', type=float, default=0.25, help='置信度阈值')
    parser.add_argument('--save', action='store_true', help='保存检测结果')
    parser.add_argument('--show', action='store_true', help='显示检测结果')
    parser.add_argument('--project', type=str, help='项目文件夹')
    parser.add_argument('--name', type=str, help='实验名称')
    
    args = parser.parse_args()
    
    # 检查模型文件是否存在
    if not os.path.exists(args.model):
        print(f"错误: 模型文件 {args.model} 不存在")
        return False
    
    # 检查输入源是否存在
    if not os.path.exists(args.source):
        print(f"错误: 输入源 {args.source} 不存在")
        return False
    
    print(f"模型: {args.model}")
    print(f"输入源: {args.source}")
    print(f"置信度: {args.conf}")
    
    # 使用CLI接口运行检测
    return run_yolo_cli(
        model_path=args.model,
        source=args.source,
        conf=args.conf,
        save=args.save,
        show=args.show,
        project=args.project,
        name=args.name
    )

def test_with_sample():
    """
    使用示例图片进行测试
    """
    print("使用示例图片进行测试...")
    
    # 查找可用的模型文件
    model_files = ['yolo11n.pt', 'yolo11s.pt', 'yolo11m.pt', 'yolo11l.pt', 'yolo11x.pt']
    model_path = None
    
    for model in model_files:
        if os.path.exists(model):
            model_path = model
            break
    
    if not model_path:
        print("错误: 找不到可用的模型文件")
        return False
    
    # 查找可用的测试图片
    test_sources = [
        "test.jpg",
        "datasets/images/test",
        "datasets/images",
        "ultralytics/assets"
    ]
    
    source_path = None
    for source in test_sources:
        if os.path.exists(source):
            source_path = source
            break
    
    if not source_path:
        print("错误: 找不到可用的测试图片")
        return False
    
    print(f"使用模型: {model_path}")
    print(f"使用测试源: {source_path}")
    
    # 运行检测
    return run_yolo_cli(
        model_path=model_path,
        source=source_path,
        conf=0.25,
        save=True,
        show=False,
        project="runs/detect",
        name="test"
    )

if __name__ == "__main__":
    if len(sys.argv) == 1:
        # 如果没有参数，运行测试
        print("没有提供参数，运行测试模式...")
        success = test_with_sample()
        if success:
            print("测试成功！")
        else:
            print("测试失败！")
    else:
        # 运行正常的检测
        success = run_yolo_direct()
        if not success:
            sys.exit(1)
