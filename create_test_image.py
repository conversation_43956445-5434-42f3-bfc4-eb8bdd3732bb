#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试图片
"""

import cv2
import numpy as np
import os

def create_test_image():
    """创建一个简单的测试图片"""
    # 创建一个640x640的彩色图片
    img = np.zeros((640, 640, 3), dtype=np.uint8)
    
    # 添加一些颜色和形状
    cv2.rectangle(img, (100, 100), (300, 300), (0, 255, 0), -1)  # 绿色矩形
    cv2.circle(img, (400, 400), 100, (255, 0, 0), -1)  # 蓝色圆形
    cv2.rectangle(img, (200, 400), (500, 500), (0, 0, 255), -1)  # 红色矩形
    
    # 保存图片
    test_path = "test_image.jpg"
    cv2.imwrite(test_path, img)
    print(f"测试图片已创建: {test_path}")
    
    return test_path

def test_with_simple_image():
    """使用简单图片测试"""
    from ultralytics import YOLO
    
    # 创建测试图片
    test_path = create_test_image()
    
    # 加载模型
    model = YOLO("yolo11n.pt")
    print("模型加载成功")
    
    try:
        # 预测
        results = model.predict(test_path, conf=0.25, verbose=False)
        print(f"预测成功，结果数量: {len(results)}")
        
        if len(results) > 0:
            # 绘制结果
            annotated_img = results[0].plot()
            print(f"绘制成功，图片形状: {annotated_img.shape}")
            
            # 保存结果
            save_path = "test_result_simple.jpg"
            cv2.imwrite(save_path, annotated_img)
            print(f"结果已保存至: {save_path}")
            
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_with_simple_image()
    if success:
        print("简单图片测试成功！")
    else:
        print("简单图片测试失败")
