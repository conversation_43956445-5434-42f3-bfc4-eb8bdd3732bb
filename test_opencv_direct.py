#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试OpenCV
"""

# 在导入ultralytics之前先导入并保存原生OpenCV函数
import cv2 as cv2_original
import numpy as np

# 保存原生函数
original_imread = cv2_original.imread
original_imwrite = cv2_original.imwrite
original_rectangle = cv2_original.rectangle
original_circle = cv2_original.circle

def create_test_image_native():
    """使用原生OpenCV创建测试图片"""
    # 创建一个640x640的彩色图片
    img = np.zeros((640, 640, 3), dtype=np.uint8)
    
    # 添加一些颜色和形状
    original_rectangle(img, (100, 100), (300, 300), (0, 255, 0), -1)  # 绿色矩形
    original_circle(img, (400, 400), 100, (255, 0, 0), -1)  # 蓝色圆形
    original_rectangle(img, (200, 400), (500, 500), (0, 0, 255), -1)  # 红色矩形
    
    # 保存图片
    test_path = "test_image_native.jpg"
    original_imwrite(test_path, img)
    print(f"测试图片已创建: {test_path}")
    
    return test_path

def test_native_opencv():
    """测试原生OpenCV"""
    print("测试原生OpenCV...")
    
    # 创建测试图片
    test_path = create_test_image_native()
    
    # 读取图片
    img = original_imread(test_path)
    if img is not None:
        print(f"成功读取图片，形状: {img.shape}")
        return test_path
    else:
        print("无法读取图片")
        return None

def test_yolo_with_native_image():
    """使用原生图片测试YOLO"""
    # 先测试原生OpenCV
    test_path = test_native_opencv()
    if test_path is None:
        return False
    
    # 现在导入ultralytics
    from ultralytics import YOLO
    
    # 加载模型
    model = YOLO("yolo11n.pt")
    print("模型加载成功")
    
    try:
        # 预测
        results = model.predict(test_path, conf=0.25, verbose=False)
        print(f"预测成功，结果数量: {len(results)}")
        
        if len(results) > 0:
            # 绘制结果
            annotated_img = results[0].plot()
            print(f"绘制成功，图片形状: {annotated_img.shape}")
            
            # 保存结果
            save_path = "test_result_native.jpg"
            original_imwrite(save_path, annotated_img)
            print(f"结果已保存至: {save_path}")
            
        return True
        
    except Exception as e:
        print(f"YOLO测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_yolo_with_native_image()
    if success:
        print("原生图片YOLO测试成功！")
    else:
        print("原生图片YOLO测试失败")
