#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

import sys
import os

# 测试基本的图片读取
def test_basic_opencv():
    print("测试基本OpenCV功能...")
    
    # 不导入ultralytics，直接使用opencv
    import cv2
    
    image_path = "datasets/images/test/1616.rf.313fed755a67aebe1734991d7a4c3b81.jpg"
    print(f"图片路径: {image_path}")
    print(f"文件存在: {os.path.exists(image_path)}")
    
    # 使用原生opencv
    img = cv2.imread(image_path)
    if img is not None:
        print(f"成功读取图片，形状: {img.shape}")
        return True
    else:
        print("无法读取图片")
        return False

def test_ultralytics_import():
    print("\n测试ultralytics导入...")
    try:
        from ultralytics import YOLO
        print("ultralytics导入成功")
        return True
    except Exception as e:
        print(f"ultralytics导入失败: {e}")
        return False

def test_model_loading():
    print("\n测试模型加载...")
    try:
        from ultralytics import YOLO
        model = YOLO("yolo11n.pt")
        print("模型加载成功")
        return model
    except Exception as e:
        print(f"模型加载失败: {e}")
        return None

if __name__ == "__main__":
    # 测试步骤
    if not test_basic_opencv():
        print("基本OpenCV测试失败")
        sys.exit(1)
    
    if not test_ultralytics_import():
        print("ultralytics导入失败")
        sys.exit(1)
    
    model = test_model_loading()
    if model is None:
        print("模型加载失败")
        sys.exit(1)
    
    print("\n所有基本测试通过！")
