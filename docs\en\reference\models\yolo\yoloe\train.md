---
description: Learn about <PERSON><PERSON>OE enhanced visual prompting (EVP) predictors in Ultralytics, which enable object detection and segmentation models to use visual prompts during inference for improved performance.
keywords: YOLOE, EVP, visual prompts, computer vision, object detection, segmentation, bounding boxes, masks, predictors, YOLOEVPDetectPredictor, Y<PERSON>OEVPSegPredictor, Ultralytics, inference
---

# Reference for `ultralytics/models/yolo/yoloe/train.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/yoloe/train.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/yoloe/train.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/yolo/yoloe/train.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.yolo.yoloe.train.YOLOETrainer

<br><br><hr><br>

## ::: ultralytics.models.yolo.yoloe.train.YOLOEPETrainer

<br><br><hr><br>

## ::: ultralytics.models.yolo.yoloe.train.YOLOETrainerFromScratch

<br><br><hr><br>

## ::: ultralytics.models.yolo.yoloe.train.YOLOEPEFreeTrainer

<br><br><hr><br>

## ::: ultralytics.models.yolo.yoloe.train.YOLOEVPTrainer

<br><br>
