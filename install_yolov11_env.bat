@echo off
echo ===================================
echo YOLOv11 环境安装脚本
echo ===================================
echo.

REM 检查Python是否已安装
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [错误] 未检测到Python，请先安装Python 3.8或更高版本。
    pause
    exit /b 1
)

echo [信息] 检测到Python已安装。
echo [信息] 开始安装YOLOv11所需依赖...

REM 创建虚拟环境（可选）
echo [询问] 是否创建虚拟环境? (y/n)
set /p create_venv=

if /i "%create_venv%"=="y" (
    echo [信息] 正在创建虚拟环境...
    python -m venv yolov11_env
    call yolov11_env\Scripts\activate.bat
    echo [信息] 虚拟环境已激活。
)

REM 安装依赖
echo [信息] 正在安装必要的依赖库...

REM 升级pip
python -m pip install --upgrade pip

REM 安装PyQt5
echo [信息] 正在安装PyQt5...
pip install PyQt5

REM 安装OpenCV
echo [信息] 正在安装OpenCV...
pip install opencv-python

REM 安装ultralytics
echo [信息] 正在安装ultralytics...
pip install ultralytics

REM 安装其他依赖
echo [信息] 正在安装其他依赖...
pip install -r yolov11_requirements.txt

echo.
echo ===================================
echo 安装完成！
echo ===================================
echo.
echo 您现在可以运行 python yolov11_gui.py 启动YOLOv11检测界面。
echo.

pause