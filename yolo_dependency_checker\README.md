# YOLO依赖检查与安装插件

这个Excel插件用于检查和安装YOLO训练所需的依赖项。它可以帮助您确保所有必要的Python包都已正确安装，从而避免在运行训练脚本时出现依赖项缺失的错误。

## 功能特点

- 检查当前Python环境中已安装的包
- 与YOLO所需的依赖项进行比对
- 显示缺失的依赖项
- 一键安装缺失的依赖项
- 生成依赖项安装报告

## 安装方法

1. 确保您已安装Python 3.6或更高版本
2. 安装必要的Python包：
   ```
   pip install openpyxl pandas xlwings
   ```
3. 运行`setup.py`脚本安装插件：
   ```
   python setup.py
   ```

## 使用说明

1. 打开Excel
2. 点击「插件」选项卡
3. 点击「YOLO依赖检查器」按钮
4. 按照界面提示操作

## 注意事项

- 插件需要管理员权限才能安装缺失的依赖项
- 如果您使用的是虚拟环境，请确保在正确的环境中运行插件