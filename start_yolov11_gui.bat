@echo off
echo ===================================
echo 启动YOLOv11检测系统
echo ===================================
echo.

REM 检查是否存在虚拟环境
if exist yolov11_env\Scripts\activate.bat (
    echo [信息] 检测到虚拟环境，正在激活...
    call yolov11_env\Scripts\activate.bat
)

REM 检查必要的依赖是否已安装
python -c "import PyQt5" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [警告] 未检测到PyQt5，可能需要先运行install_yolov11_env.bat安装环境。
    echo [询问] 是否继续启动程序? (y/n)
    set /p continue=
    if /i not "%continue%"=="y" (
        echo [信息] 已取消启动。
        pause
        exit /b 1
    )
)

echo [信息] 正在启动YOLOv11检测系统...
echo [信息] 如果是首次运行，可能需要下载模型文件，请耐心等待。
echo.

python yolov11_gui.py

if %ERRORLEVEL% NEQ 0 (
    echo [错误] 程序运行出错，请检查环境配置或查看错误信息。
    pause
) else (
    echo [信息] 程序已正常退出。
)