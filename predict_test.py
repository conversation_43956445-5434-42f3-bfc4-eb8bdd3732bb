#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预测测试脚本
"""

import os
import cv2
import numpy as np
from ultralytics import YOLO

def test_prediction():
    print("测试YOLO预测...")
    
    # 加载模型
    model = YOLO("yolo11n.pt")
    print("模型加载成功")
    
    image_path = "datasets/images/test/1616.rf.313fed755a67aebe1734991d7a4c3b81.jpg"
    print(f"测试图片: {image_path}")
    
    # 方法1: 直接使用路径
    print("\n方法1: 直接使用路径")
    try:
        results = model.predict(image_path, conf=0.25, verbose=False)
        print(f"预测成功，结果数量: {len(results)}")
        
        # 尝试绘制结果
        if len(results) > 0:
            annotated_img = results[0].plot()
            print(f"绘制成功，图片形状: {annotated_img.shape}")
            
            # 保存结果
            save_path = "test_result.jpg"
            cv2.imwrite(save_path, annotated_img)
            print(f"结果已保存至: {save_path}")
            
        return True
        
    except Exception as e:
        print(f"方法1失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_prediction_with_numpy():
    print("\n方法2: 使用numpy数组")
    
    # 加载模型
    model = YOLO("yolo11n.pt")
    
    image_path = "datasets/images/test/1616.rf.313fed755a67aebe1734991d7a4c3b81.jpg"
    
    try:
        # 使用原生opencv读取
        img = cv2.imread(image_path)
        if img is None:
            print("无法读取图片")
            return False
        
        print(f"图片读取成功，形状: {img.shape}")
        
        # 预测
        results = model.predict(img, conf=0.25, verbose=False)
        print(f"预测成功，结果数量: {len(results)}")
        
        # 尝试绘制结果
        if len(results) > 0:
            annotated_img = results[0].plot()
            print(f"绘制成功，图片形状: {annotated_img.shape}")
            
            # 保存结果
            save_path = "test_result_numpy.jpg"
            cv2.imwrite(save_path, annotated_img)
            print(f"结果已保存至: {save_path}")
            
        return True
        
    except Exception as e:
        print(f"方法2失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success1 = test_prediction()
    success2 = test_prediction_with_numpy()
    
    if success1 or success2:
        print("\n至少一种方法成功！")
    else:
        print("\n所有方法都失败了")
