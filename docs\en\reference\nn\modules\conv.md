---
description: Explore detailed documentation on convolution modules like Conv, LightConv, GhostConv, and more used in Ultralytics models.
keywords: Ultralytics, convolution modules, Conv, LightConv, GhostConv, YOLO, deep learning, neural networks
---

# Reference for `ultralytics/nn/modules/conv.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/nn/modules/conv.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/nn/modules/conv.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/nn/modules/conv.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.nn.modules.conv.Conv

<br><br><hr><br>

## ::: ultralytics.nn.modules.conv.Conv2

<br><br><hr><br>

## ::: ultralytics.nn.modules.conv.LightConv

<br><br><hr><br>

## ::: ultralytics.nn.modules.conv.DWConv

<br><br><hr><br>

## ::: ultralytics.nn.modules.conv.DWConvTranspose2d

<br><br><hr><br>

## ::: ultralytics.nn.modules.conv.ConvTranspose

<br><br><hr><br>

## ::: ultralytics.nn.modules.conv.Focus

<br><br><hr><br>

## ::: ultralytics.nn.modules.conv.GhostConv

<br><br><hr><br>

## ::: ultralytics.nn.modules.conv.RepConv

<br><br><hr><br>

## ::: ultralytics.nn.modules.conv.ChannelAttention

<br><br><hr><br>

## ::: ultralytics.nn.modules.conv.SpatialAttention

<br><br><hr><br>

## ::: ultralytics.nn.modules.conv.CBAM

<br><br><hr><br>

## ::: ultralytics.nn.modules.conv.Concat

<br><br><hr><br>

## ::: ultralytics.nn.modules.conv.Index

<br><br><hr><br>

## ::: ultralytics.nn.modules.conv.autopad

<br><br>
