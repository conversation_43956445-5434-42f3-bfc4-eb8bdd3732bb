# YOLOv11 检测系统

## 项目简介

本项目是基于YOLOv11算法的目标检测可视化系统，使用PyQt5开发了一个用户友好的图形界面，支持多种检测模式，包括单张图片检测、文件夹批量检测、视频检测和摄像头实时检测。

## 功能特点

- **模型选择**：支持选择不同的YOLOv11模型文件（.pt格式）
- **单张图片检测**：选择单张图片进行检测，并显示检测结果
- **文件夹批量检测**：批量处理文件夹中的所有图片，并保存检测结果
- **视频检测**：对视频文件进行检测，可选择保存检测后的视频
- **摄像头检测**：支持本地摄像头和IP摄像头的实时检测
- **参数调整**：可调整检测置信度阈值
- **结果保存**：可选择是否保存检测结果

## 系统要求

- Windows 7/10/11 或 Linux/macOS
- Python 3.8 或更高版本
- CUDA支持（推荐，但不是必需）

## 安装指南

### 方法一：使用安装脚本（推荐，仅Windows）

1. 双击运行 `install_yolov11_env.bat` 脚本
2. 根据提示选择是否创建虚拟环境
3. 等待安装完成

### 方法二：手动安装

1. 安装必要的依赖：

```bash
pip install -r yolov11_requirements.txt
```

2. 确保已安装PyQt5和OpenCV：

```bash
pip install PyQt5 opencv-python
```

3. 安装ultralytics库：

```bash
pip install ultralytics
```

## 使用方法

1. 运行程序：

```bash
python yolov11_gui.py
```

2. 在界面中选择YOLOv11模型文件（.pt格式）并点击"加载模型"
3. 根据需要选择检测模式：
   - **单张图片检测**：选择图片文件并点击"检测图片"
   - **文件夹批量检测**：选择图片文件夹并点击"批量处理文件夹"
   - **视频检测**：选择视频文件并点击"开始检测"
   - **摄像头检测**：选择摄像头类型（本地/IP），设置ID或地址，点击"开始检测"

## 模型文件

本项目默认使用以下YOLOv11模型文件：

- yolo11n-seg.pt（轻量级模型）
- yolo11s-seg.pt（小型模型）
- yolo11m-seg.pt（中型模型）
- yolo11l-seg.pt（大型模型）
- yolo11x-seg.pt（超大型模型）

## 注意事项

1. 首次运行时，程序会自动下载所需的模型文件（如果不存在）
2. 检测结果默认保存在原文件所在目录的 `yolov11_results` 文件夹中
3. 对于大型视频文件，建议使用较小的模型（如yolo11n-seg.pt）以获得更好的实时性能
4. 如果使用GPU加速，请确保已正确安装CUDA和cuDNN

## 故障排除

- **问题**：无法加载模型
  **解决方案**：确保模型文件路径正确，并且已安装ultralytics库

- **问题**：摄像头无法打开
  **解决方案**：检查摄像头ID是否正确，或者IP摄像头地址是否可访问

- **问题**：程序运行缓慢
  **解决方案**：尝试使用较小的模型，或者降低视频分辨率

## 许可证

本项目基于MIT许可证开源。