#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终解决方案：YOLO检测脚本
通过创建独立的Python进程来避免OpenCV兼容性问题
"""

import argparse
import os
import sys
import subprocess
import tempfile
from pathlib import Path

def create_detection_script():
    """
    创建一个独立的检测脚本，在新的Python进程中运行
    """
    script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import sys
import os
from ultralytics import YOLO

def main():
    if len(sys.argv) != 4:
        print("Usage: python script.py <model_path> <source> <conf>")
        sys.exit(1)
    
    model_path = sys.argv[1]
    source = sys.argv[2]
    conf = float(sys.argv[3])
    
    print(f"Loading model: {model_path}")
    print(f"Input source: {source}")
    print(f"Confidence: {conf}")

    try:
        # Load model
        model = YOLO(model_path)
        print("Model loaded successfully")

        # Perform prediction
        results = model.predict(
            source=source,
            conf=conf,
            save=True,
            project="runs/detect",
            name="exp",
            verbose=False
        )

        print(f"Detection completed, processed {len(results)} results")

        # Output detection results
        for i, result in enumerate(results):
            if result.boxes is not None:
                num_detections = len(result.boxes)
                print(f"Image {i+1}: detected {num_detections} objects")
            else:
                print(f"Image {i+1}: no objects detected")

        print("Detection completed successfully!")
        print("Results saved in runs/detect/exp/ directory")
        
    except Exception as e:
        print(f"Error during detection: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
'''
    
    # 创建临时脚本文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write(script_content)
        return f.name

def run_detection(model_path, source, conf=0.25):
    """
    在独立的Python进程中运行检测
    """
    # 创建检测脚本
    script_path = create_detection_script()
    
    try:
        # 运行检测脚本
        cmd = [sys.executable, script_path, model_path, source, str(conf)]
        print(f"运行命令: {' '.join(cmd)}")
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd=os.getcwd()
        )
        
        # 输出结果
        if result.stdout:
            print("输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误信息:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("检测成功完成！")
            return True
        else:
            print("检测失败！")
            return False
            
    finally:
        # 清理临时文件
        try:
            os.unlink(script_path)
        except:
            pass

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='YOLOv11检测脚本（最终解决方案）')
    parser.add_argument('--model', type=str, default='yolo11n.pt', help='模型文件路径')
    parser.add_argument('--source', type=str, help='输入源（图片、视频、文件夹）')
    parser.add_argument('--conf', type=float, default=0.25, help='置信度阈值')
    
    args = parser.parse_args()
    
    # 如果没有指定source，使用测试模式
    if not args.source:
        print("未指定输入源，使用测试模式...")
        
        # 查找可用的模型文件
        model_files = ['yolo11n.pt', 'yolo11s.pt', 'yolo11m.pt', 'yolo11l.pt', 'yolo11x.pt']
        model_path = None
        
        for model in model_files:
            if os.path.exists(model):
                model_path = model
                break
        
        if not model_path:
            print("错误: 找不到可用的模型文件")
            return False
        
        # 查找可用的测试图片
        test_sources = [
            "test.jpg",
            "datasets/images/test",
            "datasets/images",
        ]
        
        source_path = None
        for source in test_sources:
            if os.path.exists(source):
                source_path = source
                break
        
        if not source_path:
            print("错误: 找不到可用的测试图片")
            return False
        
        print(f"使用模型: {model_path}")
        print(f"使用测试源: {source_path}")
        
        # 运行检测
        return run_detection(model_path, source_path, args.conf)
    
    else:
        # 检查模型文件是否存在
        if not os.path.exists(args.model):
            print(f"错误: 模型文件 {args.model} 不存在")
            return False
        
        # 检查输入源是否存在
        if not os.path.exists(args.source):
            print(f"错误: 输入源 {args.source} 不存在")
            return False
        
        print(f"模型: {args.model}")
        print(f"输入源: {args.source}")
        print(f"置信度: {args.conf}")
        
        # 运行检测
        return run_detection(args.model, args.source, args.conf)

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
    else:
        print("\n程序执行完成！")
