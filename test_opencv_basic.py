#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试基本OpenCV功能
"""

import cv2
import numpy as np

def test_basic_opencv():
    """测试基本OpenCV功能"""
    print("测试基本OpenCV功能...")
    
    # 创建一个简单的数组
    img = np.zeros((100, 100, 3), dtype=np.uint8)
    print(f"创建数组成功，形状: {img.shape}")
    
    # 测试copyMakeBorder
    try:
        result = cv2.copyMakeBorder(img, 10, 10, 10, 10, cv2.BORDER_CONSTANT, value=(114, 114, 114))
        print(f"copyMakeBorder成功，结果形状: {result.shape}")
    except Exception as e:
        print(f"copyMakeBorder失败: {e}")
    
    # 测试imread
    try:
        img2 = cv2.imread("test.jpg")
        if img2 is not None:
            print(f"imread成功，形状: {img2.shape}")
            
            # 测试对读取的图片使用copyMakeBorder
            try:
                result2 = cv2.copyMakeBorder(img2, 10, 10, 10, 10, cv2.BORDER_CONSTANT, value=(114, 114, 114))
                print(f"对读取图片的copyMakeBorder成功，结果形状: {result2.shape}")
            except Exception as e:
                print(f"对读取图片的copyMakeBorder失败: {e}")
        else:
            print("imread返回None")
    except Exception as e:
        print(f"imread失败: {e}")

if __name__ == "__main__":
    test_basic_opencv()
